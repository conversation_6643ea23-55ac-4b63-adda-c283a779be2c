<template>
  <div :class="[b(), className]" :style="styleSizeName" @mouseover="handleMouseOver" @mouseleave="handleMouseLeave">
    <el-table class="custom-table-header" :class="option.roundedTable?'rounded-table':''" :style="styleChartName" ref="table" @cell-click="cellClick" :data="dataChart" :height="height"
      :border="option.border" :cellStyle="cellStyle" :row-style="rowStyle" :show-header="showHeader"
      :header-row-style="headerRowStyle" :header-cell-style="headerCellStyle" :span-method="option.enableMergeColumn ? spanMethod : null">
     
      <el-table-column type="index" label="#" header-align="center" align="center" v-if="option.index"
        :width="!!option.indexWidth ? option.indexWidth : 80">
        <div :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px"}' slot-scope="{$index}">{{ $index + 1 }}</div>
      </el-table-column>
      <template v-for="(item, index) in option.column">
        <el-table-column v-if="item.hide !== true" show-overflow-tooltip :key='index' :prop="item.prop"
          :label="item.label" :width="item.width">
          <template slot-scope="{row}">
            <template v-if="item.wordBreak == true">
               <!-- NEW 单元格自动换行 -->
                <div  v-html="getFormatter(item, row)" v-if="item.formatter && reload"></div>
                <div class="cell-word-break" :style='{ color: getColor(item, row), background: getbackColor(item, row) }' v-else>
                    <!--隐藏字段，无法作为判断，但可以设置宽为1隐藏列代替-->
                  <span v-if="!(item?.width =='1')"> {{ row[item.prop] }}</span>
                </div>
            </template>
            <template v-else>
              <div  :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px"}' v-html="getFormatter(item, row)" v-if="item.formatter && reload"></div>
                <div  :style='{ lineHeight:cellHeight + "px", height: cellHeight + "px", color: getColor(item, row), background: getbackColor(item, row) }' v-else>
                    <!--隐藏字段，无法作为判断，但可以设置宽为1隐藏列代替-->
                  <span v-if="!(item?.width =='1')"> {{ row[item.prop] }}</span>
                </div>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div v-if="tableCellStyle" v-html="tableCellStyle"></div>
  </div>
</template>

<script>
import create from "../../create";
import { getFunction } from '@/utils/utils';
export default create({
  name: "table",
  data() {
    return {
      tableCellStyle:null,
      reload: true,
      headerHeight: '',
      scrollCheck: null,

    };
  },
  watch: {
    'option.column'() {
      var num = 0
      num++
      this.reload = false
      this.$nextTick(() => {
        this.reload = true

      })
      setTimeout(() => {

        this.headerHeight = this.headerHeight + num
      }, 600);
    },
    scrollSpeed() {
      this.setTime();
    },
    scroll: {
      handler() {
        this.setTime();
      },
    },
    // dataChart:{
    //   handler(n,o){
    //      if(n){
    //       console.log("dataChart change#######################:",n)
    //      }
    //   },
    //   deep:true
    // }

  },
  computed: {
    showHeader() {
      return this.option.showHeader
    },
    scrollTime() {
      return this.option.scrollTime
    },
    scrollSpeed() {
      return this.option.scrollSpeed || 1
    },
    scroll() {
      return this.option.scroll
    },
    cellHeight() {
      return parseInt((this.height - this.headerHeight) / this.option.count)
    }
  },
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  mounted() {
  },
  created() {
    this.$nextTick(() => {
      this.headerHeight = this.$refs.table.$refs.headerWrapper ? parseInt(this.$refs.table.$refs.headerWrapper.clientHeight) : 0
      setTimeout(async() => {
        await this.setTime();
        //滚动间隔
      }, this.scrollTime)
    })
  },
  methods: {
    // 合并列方法
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!this.option.enableMergeColumn) {
        return [1, 1];
      }

      // 获取当前列的配置
      const currentColumn = this.option.column.find(col => col.prop === column.property);
      if (!currentColumn || !currentColumn.mergeColumn) {
        return [1, 1];
      }

      // 计算合并的行数
      const prop = column.property;
      const currentValue = row[prop];

      // 如果当前值为空或undefined，不合并
      if (currentValue === null || currentValue === undefined || currentValue === '') {
        return [1, 1];
      }

      // 查找连续相同值的行数
      let rowspan = 1;
      let colspan = 1;

      // 向下查找相同值
      for (let i = rowIndex + 1; i < this.dataChart.length; i++) {
        if (this.dataChart[i][prop] === currentValue) {
          rowspan++;
        } else {
          break;
        }
      }

      // 向上查找，如果上一行有相同值，则当前行不显示（rowspan = 0）
      for (let i = rowIndex - 1; i >= 0; i--) {
        if (this.dataChart[i][prop] === currentValue) {
          return [0, 0]; // 不显示当前单元格
        } else {
          break;
        }
      }

      return [rowspan, colspan];
    },

    getColor(item, row) {
      //debugger
    let color = this.getColorByType(item, row, 'cellfont')
    if(!color){
       color = this.getEditableTabsFormJSON(color,item, row,'cellfont')
    }
       //console.log("cellfont",color)
      return color
    },
    // 获取表格tab项条件控件
    getEditableTabsFormJSON(color,item, row,typeName='cellbackground') {
      //debugger
      let default_color = color
      let _list = item.editableTabsFormJSON
      if (_list && _list.length > 0) {
        _list.forEach((tabItem => {
          tabItem.prop =  item.prop // 补上缺少的字段，必须是同一个字段值比较
          default_color = this.getColorByType(tabItem, row, typeName)
        }))
      }
      return default_color
    },
    getbackColor(item, row) {
      let color = this.getColorByType(item, row, 'cellbackground')
      if(!color){
         color = this.getEditableTabsFormJSON(color,item, row,'cellbackground')
      }
      //console.log("cellbackground",color)
      return color
    },

    getColorByType(item, row,typeName='cellbackground'){
      // codition: [{ label: '大于', value: 1 }, { label: '小于', value: 2 },
      // { label: '等于', value: 3 }, { label: '包含', value: 4 },
      // { label: '大于等于', value: 5 },{ label: '小于等于', value: 6 },
      // { label: '不等于', value: 7 }, { label: '不包含', value: 8 },
      // ],
      let color = null
      //debugger
      if (!!item.condition && !!item.value) {
        //debugger
        //大于
        if (item.condition == 1) {

          if (row[item.prop] > item.value) {
             console.log("大于",row[item.prop], item.value)
            color = item[typeName]
            //debugger
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
             // debugger
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }
          }
        } 
        //小于
        else if (item.condition == 2) {
          if (row[item.prop] < item.value) {
            console.log("小于",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }
          }

        } 
        // 等于
        else if (item.condition == 3) {
          if (row[item.prop] == item.value) {
            console.log("等于",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }

          }
        } 
        //大于等于
        else if (item.condition == 5) {
          if (row[item.prop] >= item.value) {
            console.log("大于等于",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }

          }
        }
          //小于等于
          else if (item.condition == 6) {
          if (row[item.prop] <= item.value) {
            console.log("小于等于",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }

          }
        }
        // 不等于
        else if (item.condition == 7) {
          if (row[item.prop] != item.value) {
            console.log("不等于",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }

          }
        } 
        //不包含
        else if (item.condition == 8) {
          if (!row[item.prop].includes(item.value)) {
            console.log("不包含",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }
          }
        } 
        else {
          //包含
          if (row[item.prop].includes(item.value)) {
            console.log("包含",row[item.prop], item.value)
            color = item[typeName]
            if (!!item.rowbackground) {
              this.$set(row, 'rowbackground', item.rowbackground)
            }
            if (!!item.rowfont) {
              this.$set(row, 'rowfont', item.rowfont)
            }
            if (!!item.cellfont) {
              this.$set(row, 'cellfont', item.cellfont)
            }
            if (!!item.cellbackground) {
              this.$set(row, 'cellbackground', item.cellbackground)
            }
          }
        }
      }

      return color
    },

    getFormatter(item, row) {
      return getFunction(item.formatter)(item, row)
    },
    handleMouseOver() {
      clearInterval(this.scrollCheck);
    },
    handleMouseLeave() {
      this.setTime()
    },
    cellClick(row, column, cell, event) {
      this.updateClick(row);
      this.clickFormatter && this.clickFormatter({
        type: column,
        item: row,
        data: this.dataChart
      }, this.getItemRefs());
    },
    // 延时执行
    sleep(time = 1000) {
        const promise = new Promise((resolve) => {
            setTimeout(() => {
                resolve(true);
            }, time);
        });
        return promise;
    },
   async setTime() {
        //延时执行
        await this.sleep(3000)
      clearInterval(this.scrollCheck);
      this.headerHeight = this.$refs.table.$refs.headerWrapper ? parseInt(this.$refs.table.$refs.headerWrapper.clientHeight) : 0
      const table = this.$refs.table
      const divData = table.bodyWrapper
      // 滚动速度
      const speed = this.scrollSpeed
      // 第一行，停顿几秒
      let top = 0
    
      // 开启滚动
      if (this.scroll) {
        this.scrollCheck = setInterval(() => {
          top = top + speed
          divData.scrollTop += speed;
          let h1= divData.clientHeight + divData.scrollTop
          let h2= divData.scrollHeight
          if (h1+10 >= h2) {
            // console.log('h1 >= h2 reset scrollTop=0')
              // 最后一行，停顿几秒
              clearInterval(this.scrollCheck);
              setTimeout(async() => {
                divData.scrollTop = 0
                await this.setTime()
              }, 3000)
          }
        
          if (top >= this.cellHeight && this.scrollTime) {
            divData.scrollTop = divData.scrollTop - (top - this.cellHeight)
            clearInterval(this.scrollCheck);
            setTimeout(async() => {
              await this.setTime()
              //滚动间隔
            }, this.scrollTime)
          }
        }, 20)
      } else {
        divData.scrollTop = 0
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      //console.warn("cellStyle row?.rowbackground:",row?.rowbackground)
      let _defaultColor = row?.rowfont ? row?.rowfont : this.option.bodyColor
      if (!!row["font_Color"]) {
        // 自定义逻辑颜色 比如 格式化中添加如下，宽度为：1, 名称为空，key值:font_color【任意】
        _defaultColor = row["font_Color"]
      }

      return {
        padding: 0,
        height: this.setPx(this.cellHeight),
        fontSize: this.setPx(this.option.bodyFontSize),
        color: _defaultColor,
        textAlign: column.type == 'index' ? 'center' : this.option.bodyTextAlign,
        backgroundColor: row?.rowbackground ? row?.rowbackground : rowIndex % 2 == 0 ? this.option.othColor : this.option.nthColor,

      }
    },
    rowStyle({ row, column, rowIndex }) {
      console.warn("rowStyle row?.rowbackground:",row?.rowbackground)
      let _defaultColor = row?.rowfont ? row?.rowfont : this.option.bodyColor
      return {
        backgroundColor: row?.rowbackground ? row?.rowbackground : rowIndex % 2 == 0 ? this.option.othColor : this.option.nthColor,
        color: _defaultColor,
      }
    },
    headerRowStyle() {
      let _headerRowStyle = {
        backgroundColor: this.option.headerBackground
      }
      if(this.option.headerColHeight){
        _headerRowStyle.height = this.setPx(this.option.headerColHeight)//NEW 列头高度设置
      }
      return _headerRowStyle
    },
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      let _headerCellStyle ={
        fontSize: this.setPx(this.option.headerFontSize),
        backgroundColor: this.option.headerBackground,
        color: this.option.headerColor,
        textAlign: column.type == 'index' ? 'center' : this.option.headerTextAlign
      }
      if(this.option.headerFontSize){
        // 特殊样式处理 列头高度设置
        this.tableCellStyle =`<style type="text/css">
            .el-table .cell{
                  line-height: ${this.option.headerFontSize+3}px !important;
                }
            </style>`
      }
     
      return _headerCellStyle
    }
  }
});
</script>
<style lang="scss" scoped>
.rounded-table {
  border-radius: 15px;
  overflow: hidden;
}
.cell-word-break {
  white-space: pre-line;
  word-break: break-all;
}
/* 添加一个自定义类来控制表头高度 el-table__header-wrapper el-table__header*/ 
// ::v-deep .custom-table-header {
//   .el-table__header  tr {
//     height: 150px; /* 你想要的表头高度 */ 
//   }
// }

</style>
