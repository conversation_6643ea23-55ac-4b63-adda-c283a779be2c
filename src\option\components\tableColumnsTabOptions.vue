<template>
    <div>
        <el-form ref="form" :model="form" label-suffix=":" label-width="120px">
            <div style="display: flex">
                <el-form-item label="行背景">
                    <avue-input-color  type="textarea" placeholder="请选择颜色"
                        v-model="form.rowbackground"></avue-input-color>
                </el-form-item>
                <el-form-item label="行字体">
                    <avue-input-color  type="textarea" placeholder="请选择颜色" v-model="form.rowfont"></avue-input-color>
                </el-form-item>
            </div>

            <div style="display: flex">
                <el-form-item label="单元格背景">
                    <avue-input-color  type="textarea" placeholder="请选择颜色"
                        v-model="form.cellbackground"></avue-input-color>
                </el-form-item>
                <el-form-item label="单元格字体" width="160">
                    <avue-input-color  type="textarea" placeholder="请选择颜色" v-model="form.cellfont"></avue-input-color>
                </el-form-item>
            </div>
            <div style="display: flex">
                <el-form-item label="条件">
                    <avue-select @change="conditionChange" placeholder="请选择条件" v-model="form.condition" :dic="formCondictionList"></avue-select>
                </el-form-item>
                <el-form-item label="值">
                    <avue-input placeholder="请输入值" type="text" v-model="form.value"></avue-input>
                </el-form-item>
            </div>

            <el-form-item label="格式化">
                <template slot="label">
                    格式化
                    <el-tooltip effect="dark" content='根据不同条件，显示不同的行字体颜色案例 比如在列【化验结果】编辑,必须在数据列表存在font_Color字段，或在数据查询中过滤器添加字段：font_Color：
                                        (data,params,refs)=>{
                                            let newData = data.map(item=>{
                                                item["font_Color"]=""
                                                return item
                                            })
                                            return newData
                                        }
                                        根据不同条件：
                                        (name,data)=>{
                                          if(data["班次"]=="白班"){
                                              data.font_Color="red"
                                          }
                                          if(data["班次"]=="晚班"){
                                              data.font_Color="green" 
                                          }
                                        return data["化验结果"]
                                      }' placement="top">
                        <i class="el-icon-info"></i>
                    </el-tooltip>
                </template>
                <MonacoEditor disabled v-model="form.formatter" language="javascript"  height="100px"></MonacoEditor>
                 <el-button size="small" type="primary" icon="el-icon-edit"
                    @click="openCode('formatter', '格式化')">编辑</el-button>
                <!-- <codeedit @submit="codeClose" :title="code.title" v-model="code.obj" v-if="code.box" :type="code.type"
                :visible.sync="code.box"></codeedit> -->

            </el-form-item>
        </el-form>
       
    </div>
</template>
<script>
import { dicOption } from "@/option/config";
import MonacoEditor from "@/page/components/editor";
import codeedit from "../../page/group/code";
export default {
    name: 'tableColumnsTabOptions',
    inject: ["main"],
    components: {
        codeedit,
        MonacoEditor,
    },
    props: {
        editableTabs: {
            type: Array,
            default: () => []
        },
        tabItem: {
            type: Object,
            default: () => {

            }
        },
        firstTabCondition: {
            type: Number,
            default: 0
        },
        tabIndex: {
            type: Number,
            default: 0
        },
        dataForm: {
            type: Object,
            default: () => {

            }
    }
},
    data() {
        return {
            newChangeCondition: [],
           // oldChangeCondition:[],
            dicOption: dicOption,
            coditionList:Object.assign([],dicOption.codition),
            form:{
                rowbackground:"", 
                rowfont:"",
                cellbackground:"",
                cellfont:"",
                condition :"",
                value :"",
                formatter:"",
            },
            columnsOptions: [],
            code: {
                box: false,
                type: "",
                obj: {},
            },
        }
    },
    computed: {
      formCondictionList(){
        let _list = []
         if(this.$store.state.globalConditionList && this.$store.state.globalConditionList.length>0){
          _list = this.$store.state.globalConditionList
         }
        return _list
      }
  },
    watch:{
        formCondictionList:{
            handler(val) {
                if(val && val.length>0){
                console.log("========formCondictionList change222...======")
                }
                
            },
            deep: true
        },
        form:{
            handler(val){
                //console.log("form value Change:",val)
                this.$emit("update:tabItemChange",val)
            },
            deep:true
        },
        dataForm:{
            handler(val){
                if(val && Object.keys(val).length>0){
                    try {
                        let _formData = val.editableTabsFormJSON[this.tabIndex]
                        this.form = Object.assign({},this.form,_formData)
                        //this.cloneOldChangeCondition()
                        //this.coditionListFn()
                    } catch (error) {
                        
                    }
    
                }
            },
            deep:true
        }
    },
    methods:{
        cloneOldChangeCondition(){
           let _self = this
            this.newChangeCondition =[
                 {
                        tabIndex:0,
                        condictionValue:this.dataForm.condition,
                }
            ]
            this.dataForm.editableTabsFormJSON.forEach(item=>{
               // debugger
                let newCondictionItem ={
                        tabIndex:_self.tabIndex+1,
                        condictionValue:item.condition,
                }
                this.newChangeCondition.push(newCondictionItem)
            })
        },
        conditionChange(params){
            let curTabIndex = this.tabIndex+1
            let oldList = [...this.$store.state.globalConditionList]
           // let existIndex = newList.findIndex(item=>item.tabIndex == curTabIndex)
            let newList = oldList.map(item=>{
               // debugger
                    if(item.tabIndex == curTabIndex){
                        item.tabIndex = -1
                        item.disabled = false
                    }
                    if(item.value == params.value){
                        item.tabIndex = curTabIndex
                        item.disabled = true
                    }
                    return item
            })
            // debugger
            this.$store.commit("set_globalConditionList", newList);
            //this.coditionListFn()
        },
         // 已经选过的条件，不能再选择，禁用已经选择过的条件
    coditionListFn(){
        //debugger
      let _self = this
      let _coditionList =  [...dicOption.codition]
      try {
        if(this.dataForm && this.dataForm.editableTabsFormJSON.length>0){
          _coditionList =  _coditionList.map((item=>{
             item.disabled = false
             let isSelectedIndex = _self.dataForm.editableTabsFormJSON.findIndex((tabItem,currentIndex)=>{
                
                return (tabItem.condition == item.value && currentIndex!= _self.tabIndex)
            })
            // debugger
              if(isSelectedIndex>-1){
                item.disabled = true
                //item.desc = '已选'
              }
              // 第一个TAB的选择条件
              if(item.value && item.value == _self.firstTabCondition){
                item.disabled = true
                //item.desc = '已选'
              }
              return item
          }))
        }
      } catch (error) {
        
      }
      this.coditionList = _coditionList
    },
        formDataChange(fieldName,e){
            //console.log("fieldName value Change:",fieldName)
        },
        openCode(type, title) {
            let params ={
                type, 
                title,
                formatter:this.form.formatter,
                isMain:false,
            }
            this.$emit("openCode", params);
        },
        codeClose(value) {
            if (this.code.type == "formatter") {
                this.form.formatter = value;
            } else {
                //this.main.activeOption.column = value;
            }
        },
    }
}
</script>
<style scoped lang="scss">
    ::v-deep .monaco_editor_container {
        .monaco-editor{
            width: 587px !important;
            height: 100px !important;
        }
  }
</style>